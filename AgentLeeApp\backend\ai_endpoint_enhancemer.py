# ai_endpoint_enhancer.py
import os
import re
import json
import glob
import dotenv
import requests

dotenv.load_dotenv()

# --- CONFIGURATION ---
MCP_PATH = os.getenv("MCP_PATH", "backend/mcphub/mcp")
AI_PROVIDER = os.getenv("AI_PROVIDER", "gemini")  # Options: "gemini", "openai"
API_KEY = os.getenv("GEMINI_API_KEY") or os.getenv("OPENAI_API_KEY")
USE_DOC_PATCHING = True

# --- CORE FUNCTION ---
def describe_endpoint_with_ai(code: str, filename: str) -> dict:
    prompt = f"""
You are an expert Python developer.

Below is a FastAPI-style endpoint file named `{filename}`.

1. Generate a short but clear description for what this endpoint does.
2. Infer the expected input schema (fields and types).
3. Suggest improvements if anything is missing (e.g., no health check, vague logic).
4. Output a JSON block like:
{{
  "description": "...",
  "input_schema": {{ "field1": "type", "field2": "type" }},
  "recommendations": ["..."]
}}

Code:
    """
    if AI_PROVIDER == "gemini":
        return call_gemini(prompt)
    elif AI_PROVIDER == "openai":
        return call_openai(prompt)
    else:
        raise ValueError("Invalid AI provider.")

# --- LLM INTEGRATION ---
def call_gemini(prompt: str) -> dict:
    url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent"
    headers = { "Content-Type": "application/json" }
    payload = {
        "contents": [{"parts": [{"text": prompt}]}],
        "generationConfig": { "temperature": 0.3, "topK": 1, "topP": 0.95 }
    }
    resp = requests.post(f"{url}?key={API_KEY}", headers=headers, json=payload)
    text = resp.json()['candidates'][0]['content']['parts'][0]['text']
    try:
        return json.loads(text)
    except:
        return { "description": "(Failed to parse)", "raw_output": text }

def call_openai(prompt: str) -> dict:
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    payload = {
        "model": "gpt-4",
        "messages": [{ "role": "user", "content": prompt }],
        "temperature": 0.3
    }
    resp = requests.post("https://api.openai.com/v1/chat/completions", headers=headers, json=payload)
    text = resp.json()['choices'][0]['message']['content']
    try:
        return json.loads(text)
    except:
        return { "description": "(Failed to parse)", "raw_output": text }

# --- PATCHER ---
def patch_description_in_file(file_path: str, description: str):
    with open(file_path, "r", encoding="utf-8") as f:
        lines = f.readlines()

    if '"""' in lines[0]:
        print(f"[i] Docstring already exists in: {file_path}")
        return

    lines.insert(0, f'"""{description}"""\n\n')
    with open(file_path, "w", encoding="utf-8") as f:
        f.writelines(lines)
    print(f"[+] Patched docstring into {file_path}")

# --- MAIN SCANNER ---
def run_analysis():
    result = {}
    py_files = glob.glob(f"{MCP_PATH}/**/*.py", recursive=True)

    for fpath in py_files:
        with open(fpath, "r", encoding="utf-8") as f:
            code = f.read()

        info = describe_endpoint_with_ai(code, fpath)
        result[fpath] = info

        print(f"\n🧠 [{fpath}]")
        print(json.dumps(info, indent=2))

        if USE_DOC_PATCHING and "description" in info and info["description"] != "(Failed to parse)":
            patch_description_in_file(fpath, info["description"])

    with open("mcp_endpoint_analysis.json", "w") as out:
        json.dump(result, out, indent=2)

    print("\n✅ Completed enhancement pass for all MCPs.")

if __name__ == "__main__":
    run_analysis()
