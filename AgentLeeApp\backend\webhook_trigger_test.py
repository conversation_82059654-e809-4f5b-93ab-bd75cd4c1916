"""
🔔 Webhook Trigger Test: Validates all registered webhook endpoints.
"""
import requests

WEBHOOKS = [
    "http://localhost:3001/webhook/test",
    "http://localhost:3001/trigger/notifier/ping"
    # ...add all your webhook URLs here
]

for url in WEBHOOKS:
    try:
        resp = requests.post(url, json={"test": True}, timeout=3)
        print(f"{url}: {resp.status_code} {resp.text}")
    except Exception as e:
        print(f"{url}: ❌ Error: {e}")
