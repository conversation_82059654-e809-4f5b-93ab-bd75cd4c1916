"""
⏰ Trigger Scheduler: Triggers MCPs on a time/event basis.
"""
import time, requests

TRIGGERS = [
    {"mcp": "health_monitor", "url": "http://localhost:8000/health", "interval": 60},
    # ...add triggers as needed
]

while True:
    for trig in TRIGGERS:
        try:
            requests.get(trig["url"])
            print(f"Triggered {trig['mcp']}")
        except Exception as e:
            print(f"Error triggering {trig['mcp']}: {e}")
    time.sleep(60)
