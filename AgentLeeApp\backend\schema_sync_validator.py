"""
🔗 Schema Sync Validator: Compares backend and frontend schema files.
"""
import os, json

BACKEND_SCHEMA_PATH = "mcphub/mcp/schema"
FRONTEND_SCHEMA_PATH = "../../frontend/mcp/schema"

def get_json_files(path):
    for root, dirs, files in os.walk(path):
        for file in files:
            if file.endswith(".json"):
                yield os.path.join(root, file)

backend_files = list(get_json_files(BACKEND_SCHEMA_PATH))
frontend_files = list(get_json_files(FRONTEND_SCHEMA_PATH))

for bf in backend_files:
    name = os.path.basename(bf)
    match = [ff for ff in frontend_files if os.path.basename(ff) == name]
    if match:
        with open(bf, "r") as f1, open(match[0], "r") as f2:
            if f1.read() != f2.read():
                print(f"⚠️ Schema mismatch: {name}")
    else:
        print(f"❌ Backend schema missing frontend: {name}")
