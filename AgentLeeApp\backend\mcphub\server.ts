import express, { Request, Response } from 'express';
import cors from 'cors';
import path from 'path';
import { execFile, ExecFileException } from 'child_process';

const app = express();
const PORT = process.env.PORT || 8000;

// Middleware
app.use(cors());
app.use(express.json());

/**
 * POST /run-mcp
 * Accepts: { args: string[], input?: string }
 * Runs an MCP tool via ts-node and returns output
 */
app.post('/run-mcp', (req: Request, res: Response) => {
  const { args, input } = req.body;

  if (!Array.isArray(args) || args.length === 0) {
    return res.status(400).json({ error: 'No MCP tool specified.' });
  }

  const toolName = args[args.length - 1];
  const toolPath = path.resolve(__dirname, 'mcp', `${toolName}.ts`);

  console.log(`🛠️ Executing MCP tool: ${toolPath}`);

  execFile('ts-node', [toolPath, ...(input ? [input] : [])], (error: ExecFileException | null, stdout: string, stderr: string) => {
    if (error) {
      console.error(`❌ MCP Execution Failed:\n${stderr || error.message}`);
      return res.status(500).json({ error: stderr || error.message });
    }

    console.log(`✅ MCP Output:\n${stdout}`);
    res.send(stdout);
  });
});

app.listen(PORT, () => {
  console.log(`🚀 MCP Server running on port ${PORT}`);
});
