"""
🩺 MCP Health Checker: Pings all MCP `/health` endpoints and logs status.
"""
import json, time, requests

MCP_LIST = [
    {"name": "agent-lee-bridge", "url": "http://localhost:8001/health"},
    {"name": "notion", "url": "http://localhost:8003/health"},
    # ...add all your MCPs here
]

def check_health():
    results = []
    for mcp in MCP_LIST:
        try:
            res = requests.get(mcp["url"], timeout=3)
            status = "✅ OK" if res.status_code == 200 else f"⚠️ {res.status_code}"
        except Exception as e:
            status = f"❌ Error: {str(e)}"
        results.append({"mcp": mcp["name"], "status": status})
    with open("workflow_state.json", "w") as f:
        json.dump(results, f, indent=2)

if __name__ == "__main__":
    while True:
        check_health()
        print("Health check complete. See workflow_state.json.")
        time.sleep(30)
