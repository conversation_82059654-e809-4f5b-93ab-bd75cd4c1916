"""
📦 Frontend Import Validator: Checks for broken imports in .ts/.tsx files.
"""
import os

FRONTEND_DIR = "../../frontend"

for root, dirs, files in os.walk(FRONTEND_DIR):
    for file in files:
        if file.endswith((".ts", ".tsx")):
            path = os.path.join(root, file)
            with open(path, "r", encoding="utf-8") as f:
                for line in f:
                    if "from \"@/" in line or "from '@/mcp/" in line:
                        import_path = line.split("from")[1].strip().strip("\"';")
                        import_file = os.path.join(FRONTEND_DIR, import_path.replace("@/", "").replace("/", "\\") + ".ts")
                        if not os.path.exists(import_file):
                            print(f"❌ Broken import in {file}: {import_path}")
