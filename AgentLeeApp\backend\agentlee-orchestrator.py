"""
🤖 AgentLee Orchestrator: Main task dispatcher for MCP actions.
"""
import requests, json

def dispatch_mcp(name, endpoint="/run", data=None):
    url = f"http://localhost:8000/{name}{endpoint}"
    try:
        resp = requests.post(url, json=data or {}, timeout=10)
        return resp.json()
    except Exception as e:
        return {"error": str(e)}

# Example
if __name__ == "__main__":
    tasks = [
        {"mcp": "notion", "payload": {"query": "Test"}},
        # ... add tasks as needed
    ]
    for t in tasks:
        result = dispatch_mcp(t["mcp"], data=t["payload"])
        print(f"{t['mcp']} result: {result}")
