"""
🧪 Diagnostic MCP Runner: Runs test payloads on all MCPs.
"""
import requests, json

MCP_RUN_URLS = [
    "http://localhost:8001/run",
    "http://localhost:8002/run"
    # ...list your /run endpoints
]

TEST_PAYLOAD = {"test": True}

for url in MCP_RUN_URLS:
    try:
        resp = requests.post(url, json=TEST_PAYLOAD, timeout=5)
        print(f"{url}: {resp.status_code} {resp.json()}")
    except Exception as e:
        print(f"{url}: ❌ {e}")
